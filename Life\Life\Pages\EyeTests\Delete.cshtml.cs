using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.EyeTests
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int EyeTestId { get; set; }

        public string Provider { get; set; } = string.Empty;
        public DateOnly TestDate { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var eyeTest = await _context.EyeTests
                .Where(e => e.Id == id && e.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (eyeTest == null)
            {
                return NotFound();
            }

            EyeTestId = eyeTest.Id;
            Provider = eyeTest.Provider;
            TestDate = eyeTest.TestDate;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var eyeTest = await _context.EyeTests
                .Where(e => e.Id == id && e.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (eyeTest == null)
            {
                return NotFound();
            }

            // Delete the linked automatic reminder first
            var linkedReminder = await _context.Reminders
                .FirstOrDefaultAsync(r => r.EyeTestId == eyeTest.Id && r.IsAutomatic == true);

            if (linkedReminder != null)
            {
                _context.Reminders.Remove(linkedReminder);
            }

            // Delete the eye test
            _context.EyeTests.Remove(eyeTest);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Eye test deleted successfully";

            return RedirectToPage("./Index");
        }
    }
}
