using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.GlobalHealthInsuranceCards
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<GlobalHealthInsuranceCard> GlobalHealthInsuranceCards { get; set; } = default!;
        public IList<GlobalHealthInsuranceCard> ExpiredGlobalHealthInsuranceCards { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Get current (non-expired) global health insurance cards
            GlobalHealthInsuranceCards = await _context.GlobalHealthInsuranceCards
                .AsNoTracking()
                .Where(g => g.UserId == currentUser.Id)
                .Where(g => g.ExpiryDate >= DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderBy(g => g.ExpiryDate)
                .ToListAsync();

            // Get expired global health insurance cards
            ExpiredGlobalHealthInsuranceCards = await _context.GlobalHealthInsuranceCards
                .AsNoTracking()
                .Where(g => g.UserId == currentUser.Id)
                .Where(g => g.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderByDescending(g => g.ExpiryDate)
                .ToListAsync();

            return Page();
        }
    }
}
