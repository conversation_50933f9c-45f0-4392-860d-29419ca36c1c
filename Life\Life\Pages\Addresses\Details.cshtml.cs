using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Addresses
{
    public class DetailsModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DetailsModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public int AddressId { get; set; }
        public string HouseFlatNumber { get; set; } = string.Empty;
        public string StreetLineOne { get; set; } = string.Empty;
        public string? StreetLineTwo { get; set; }
        public string City { get; set; } = string.Empty;
        public string Postcode { get; set; } = string.Empty;
        public DateOnly? MoveInDate { get; set; }
        public DateOnly? MoveOutDate { get; set; }
        public DateOnly? PurchaseDate { get; set; }
        public decimal? PurchasePrice { get; set; }
        public DateOnly? SoldDate { get; set; }
        public decimal? SoldPrice { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Linked entities
        public IList<Vehicle> LinkedVehicles { get; set; } = new List<Vehicle>();
        public IList<GeneralItem> LinkedGeneralItems { get; set; } = new List<GeneralItem>();
        public IList<DrivingLicence> LinkedDrivingLicences { get; set; } = new List<DrivingLicence>();
        public IList<GpPractice> LinkedGpPractices { get; set; } = new List<GpPractice>();
        public IList<Dentist> LinkedDentists { get; set; } = new List<Dentist>();
        public IList<CurrentAccount> LinkedCurrentAccounts { get; set; } = new List<CurrentAccount>();
        public IList<CreditCard> LinkedCreditCards { get; set; } = new List<CreditCard>();
        public IList<SavingsAccount> LinkedSavingsAccounts { get; set; } = new List<SavingsAccount>();
        public IList<Loan> LinkedLoans { get; set; } = new List<Loan>();
        public IList<Mortgage> LinkedMortgages { get; set; } = new List<Mortgage>();
        public IList<Pension> LinkedPensions { get; set; } = new List<Pension>();
        public IList<LifeInsurancePolicy> LinkedLifeInsurancePolicies { get; set; } = new List<LifeInsurancePolicy>();
        public IList<TravelInsurancePolicy> LinkedTravelInsurancePolicies { get; set; } = new List<TravelInsurancePolicy>();
        public IList<GadgetInsurancePolicy> LinkedGadgetInsurancePolicies { get; set; } = new List<GadgetInsurancePolicy>();
        public IList<VehicleInsurancePolicy> LinkedVehicleInsurancePolicies { get; set; } = new List<VehicleInsurancePolicy>();
        public IList<AddressInsurancePolicy> LinkedAddressInsurancePolicies { get; set; } = new List<AddressInsurancePolicy>();
        public IList<VehicleFinanceAgreement> LinkedVehicleFinanceAgreements { get; set; } = new List<VehicleFinanceAgreement>();
        public IList<VehicleBreakdownPolicy> LinkedVehicleBreakdownPolicies { get; set; } = new List<VehicleBreakdownPolicy>();
        public IList<Tenancy> LinkedTenancies { get; set; } = new List<Tenancy>();
        public IList<UtilityBill> LinkedUtilityBills { get; set; } = new List<UtilityBill>();

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var address = await _context.Addresses
                .Where(a => a.Id == id && a.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (address == null)
            {
                return NotFound();
            }

            AddressId = address.Id;
            HouseFlatNumber = address.HouseFlatNumber;
            StreetLineOne = address.StreetLineOne;
            StreetLineTwo = address.StreetLineTwo;
            City = address.City;
            Postcode = address.Postcode;
            MoveInDate = address.MoveInDate;
            MoveOutDate = address.MoveOutDate;
            PurchaseDate = address.PurchaseDate;
            PurchasePrice = address.PurchasePrice;
            SoldDate = address.SoldDate;
            SoldPrice = address.SoldPrice;
            Notes = address.Notes;
            CreatedAt = address.CreatedAt;
            UpdatedAt = address.UpdatedAt;

            // Load linked entities
            await LoadLinkedEntities(currentUser.Id, address.Id);

            return Page();
        }

        private async Task LoadLinkedEntities(string userId, int addressId)
        {
            LinkedVehicles = await _context.Vehicles
                .Where(v => v.UserId == userId && v.AddressId == addressId)
                .OrderBy(v => v.Make).ThenBy(v => v.Model)
                .ToListAsync();

            LinkedGeneralItems = await _context.GeneralItems
                .Where(gi => gi.UserId == userId && gi.AddressId == addressId)
                .OrderBy(gi => gi.Name)
                .ToListAsync();

            LinkedDrivingLicences = await _context.DrivingLicences
                .Where(dl => dl.UserId == userId && dl.AddressId == addressId)
                .OrderBy(dl => dl.LicenceNumber)
                .ToListAsync();

            LinkedGpPractices = await _context.GpPractices
                .Where(gp => gp.UserId == userId && gp.AddressId == addressId)
                .OrderBy(gp => gp.Name)
                .ToListAsync();

            LinkedDentists = await _context.Dentists
                .Where(d => d.UserId == userId && d.AddressId == addressId)
                .OrderBy(d => d.Name)
                .ToListAsync();

            LinkedCurrentAccounts = await _context.CurrentAccounts
                .Where(ca => ca.UserId == userId && ca.AddressId == addressId)
                .OrderBy(ca => ca.Provider).ThenBy(ca => ca.Name)
                .ToListAsync();

            LinkedCreditCards = await _context.CreditCards
                .Where(cc => cc.UserId == userId && cc.AddressId == addressId)
                .OrderBy(cc => cc.Provider).ThenBy(cc => cc.Name)
                .ToListAsync();

            LinkedSavingsAccounts = await _context.SavingsAccounts
                .Where(sa => sa.UserId == userId && sa.AddressId == addressId)
                .OrderBy(sa => sa.Provider).ThenBy(sa => sa.Name)
                .ToListAsync();

            LinkedLoans = await _context.Loans
                .Where(l => l.UserId == userId && l.AddressId == addressId)
                .OrderBy(l => l.Provider).ThenBy(l => l.Name)
                .ToListAsync();

            LinkedMortgages = await _context.Mortgages
                .Where(m => m.UserId == userId && m.AddressId == addressId)
                .OrderBy(m => m.Provider).ThenBy(m => m.Name)
                .ToListAsync();

            LinkedPensions = await _context.Pensions
                .Where(p => p.UserId == userId && p.AddressId == addressId)
                .OrderBy(p => p.Provider).ThenBy(p => p.Name)
                .ToListAsync();

            LinkedLifeInsurancePolicies = await _context.LifeInsurancePolicies
                .Where(lip => lip.UserId == userId && lip.AddressId == addressId)
                .OrderBy(lip => lip.Provider).ThenBy(lip => lip.Name)
                .ToListAsync();

            LinkedTravelInsurancePolicies = await _context.TravelInsurancePolicies
                .Where(tip => tip.UserId == userId && tip.AddressId == addressId)
                .OrderBy(tip => tip.Provider).ThenBy(tip => tip.Name)
                .ToListAsync();

            LinkedGadgetInsurancePolicies = await _context.GadgetInsurancePolicies
                .Where(gip => gip.UserId == userId && gip.AddressId == addressId)
                .OrderBy(gip => gip.Provider).ThenBy(gip => gip.Name)
                .ToListAsync();

            LinkedVehicleInsurancePolicies = await _context.VehicleInsurancePolicies
                .Where(vip => vip.UserId == userId && vip.AddressId == addressId)
                .OrderBy(vip => vip.Provider).ThenBy(vip => vip.Name)
                .ToListAsync();

            LinkedAddressInsurancePolicies = await _context.AddressInsurancePolicies
                .Where(aip => aip.UserId == userId && aip.AddressId == addressId)
                .OrderBy(aip => aip.Provider).ThenBy(aip => aip.Name)
                .ToListAsync();

            LinkedVehicleFinanceAgreements = await _context.VehicleFinanceAgreements
                .Where(vfa => vfa.UserId == userId && vfa.AddressId == addressId)
                .OrderBy(vfa => vfa.Provider).ThenBy(vfa => vfa.Name)
                .ToListAsync();

            LinkedVehicleBreakdownPolicies = await _context.VehicleBreakdownPolicies
                .Where(vbp => vbp.UserId == userId && vbp.AddressId == addressId)
                .OrderBy(vbp => vbp.Provider).ThenBy(vbp => vbp.Name)
                .ToListAsync();

            LinkedTenancies = await _context.Tenancies
                .Where(t => t.UserId == userId && t.AddressId == addressId)
                .OrderBy(t => t.Name)
                .ToListAsync();

            LinkedUtilityBills = await _context.UtilityBills
                .Where(ub => ub.UserId == userId && ub.AddressId == addressId)
                .OrderBy(ub => ub.Provider).ThenBy(ub => ub.Name)
                .ToListAsync();
        }
    }
}
