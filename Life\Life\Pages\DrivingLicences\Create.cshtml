@page
@model Life.Pages.DrivingLicences.CreateModel
@{
    ViewData["Title"] = "Create Driving Licence";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Create Driving Licence</h2>
                </div>
                <div class="card-body">
                    <form method="post" class="row g-3 mt-1" autocomplete="off">

                        <div class="row my-3">
                            <small class="text-muted">Required fields are marked in <strong>bold</strong>.</small>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Name" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="Name" class="form-control" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="LicenceNumber" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="LicenceNumber" class="form-control" />
                                <span asp-validation-for="LicenceNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="StartDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="StartDate" class="form-control" type="date" />
                                <span asp-validation-for="StartDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="ExpiryDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="ExpiryDate" class="form-control" type="date" />
                                <span asp-validation-for="ExpiryDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="PhotocardStartDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="PhotocardStartDate" class="form-control" type="date" />
                                <span asp-validation-for="PhotocardStartDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="PhotocardExpiryDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="PhotocardExpiryDate" class="form-control" type="date" />
                                <span asp-validation-for="PhotocardExpiryDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="AddressId" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <select asp-for="AddressId" asp-items="Model.Addresses" class="form-select">
                                    <option value="">Select an address (optional)</option>
                                </select>
                                <span asp-validation-for="AddressId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Notes" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-sm btn-primary">Create</button>
                                <a asp-page="./Index" class="btn btn-sm btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
