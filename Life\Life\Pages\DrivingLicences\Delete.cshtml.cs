using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.DrivingLicences
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int DrivingLicenceId { get; set; }

        public string Name { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var drivingLicence = await _context.DrivingLicences
                .Where(d => d.Id == id && d.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (drivingLicence == null)
            {
                return NotFound();
            }

            DrivingLicenceId = drivingLicence.Id;
            Name = drivingLicence.Name;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var drivingLicence = await _context.DrivingLicences
                .Where(d => d.Id == id && d.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (drivingLicence == null)
            {
                return NotFound();
            }

            // Delete the linked automatic reminders first
            var linkedReminders = await _context.Reminders
                .Where(r => r.DrivingLicenceId == drivingLicence.Id && r.IsAutomatic == true)
                .ToListAsync();

            foreach (var reminder in linkedReminders)
            {
                _context.Reminders.Remove(reminder);
            }

            // Delete the driving licence
            _context.DrivingLicences.Remove(drivingLicence);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Driving licence deleted successfully";

            return RedirectToPage("./Index");
        }
    }
}
