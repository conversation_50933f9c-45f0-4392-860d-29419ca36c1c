using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.GlobalHealthInsuranceCards
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int GlobalHealthInsuranceCardId { get; set; }

        [BindProperty]
        [Required]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(50, ErrorMessage = "Personal ID Number cannot exceed 50 characters")]
        [DisplayName("Personal ID Number")]
        public string PersonalIdNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(50, ErrorMessage = "Card ID Number cannot exceed 50 characters")]
        [DisplayName("Card ID Number")]
        public string CardIdNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Start Date is required")]
        [DisplayName("Start Date")]
        public DateOnly StartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Expiry Date is required")]
        [DisplayName("Expiry Date")]
        public DateOnly ExpiryDate { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var globalHealthInsuranceCard = await _context.GlobalHealthInsuranceCards
                .Where(g => g.Id == id && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (globalHealthInsuranceCard == null)
            {
                return NotFound();
            }

            GlobalHealthInsuranceCardId = globalHealthInsuranceCard.Id;
            Name = globalHealthInsuranceCard.Name;
            PersonalIdNumber = globalHealthInsuranceCard.PersonalIdNumber;
            CardIdNumber = globalHealthInsuranceCard.CardIdNumber;
            StartDate = globalHealthInsuranceCard.StartDate;
            ExpiryDate = globalHealthInsuranceCard.ExpiryDate;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that expiry date is after start date
            if (ExpiryDate <= StartDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must be after start date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Get the existing global health insurance card
            var globalHealthInsuranceCard = await _context.GlobalHealthInsuranceCards
                .Where(g => g.Id == GlobalHealthInsuranceCardId && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (globalHealthInsuranceCard == null)
            {
                return NotFound();
            }

            // Store original values for reminder management
            var originalExpiryDate = globalHealthInsuranceCard.ExpiryDate;
            var originalName = globalHealthInsuranceCard.Name;

            // Update the global health insurance card
            globalHealthInsuranceCard.Name = Name;
            globalHealthInsuranceCard.PersonalIdNumber = PersonalIdNumber;
            globalHealthInsuranceCard.CardIdNumber = CardIdNumber;
            globalHealthInsuranceCard.StartDate = StartDate;
            globalHealthInsuranceCard.ExpiryDate = ExpiryDate;
            globalHealthInsuranceCard.UpdatedAt = DateTime.UtcNow;


            // If the expiry date or name changed, update the linked reminder
            if (originalExpiryDate != ExpiryDate || originalName != Name)
            {
                var linkedReminder = await _context.Reminders
                    .Where(r => r.GlobalHealthInsuranceCardId == GlobalHealthInsuranceCardId && r.IsAutomatic == true)
                    .FirstOrDefaultAsync();

                if (linkedReminder != null)
                {
                    linkedReminder.Name = $"{Name} - GHIC Expiry Date";
                    linkedReminder.ReminderDate = ExpiryDate;
                    linkedReminder.UpdatedAt = DateTime.UtcNow;
                }
            }


            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!GlobalHealthInsuranceCardExists(GlobalHealthInsuranceCardId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "GHIC updated successfully";

            return RedirectToPage("./Details", new { id = GlobalHealthInsuranceCardId });
        }

        private bool GlobalHealthInsuranceCardExists(int id)
        {
            return _context.GlobalHealthInsuranceCards.Any(e => e.Id == id);
        }
    }
}
