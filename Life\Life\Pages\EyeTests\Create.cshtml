@page
@model Life.Pages.EyeTests.CreateModel
@{
    ViewData["Title"] = "Create Eye Test";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Create Eye Test</h2>
                </div>
                <div class="card-body">
                    <form method="post" class="row g-3 mt-1" autocomplete="off">

                        <div class="row my-3">
                            <small class="text-muted">Required fields are marked in <strong>bold</strong>.</small>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Provider" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="Provider" class="form-control" />
                                <span asp-validation-for="Provider" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="TestDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="TestDate" class="form-control" type="date" />
                                <span asp-validation-for="TestDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="NextTestDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="NextTestDate" class="form-control" type="date" />
                                <span asp-validation-for="NextTestDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="RightSPH" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="RightSPH" class="form-control" type="number" step="0.01" />
                                <span asp-validation-for="RightSPH" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="RightCYL" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="RightCYL" class="form-control" type="number" step="0.01" />
                                <span asp-validation-for="RightCYL" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="RightAXIS" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="RightAXIS" class="form-control" type="number" step="0.01" />
                                <span asp-validation-for="RightAXIS" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="LeftSPH" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="LeftSPH" class="form-control" type="number" step="0.01" />
                                <span asp-validation-for="LeftSPH" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="LeftCYL" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="LeftCYL" class="form-control" type="number" step="0.01" />
                                <span asp-validation-for="LeftCYL" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="LeftAXIS" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="LeftAXIS" class="form-control" type="number" step="0.01" />
                                <span asp-validation-for="LeftAXIS" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Cost" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Cost" class="form-control" type="number" />
                                <span asp-validation-for="Cost" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Notes" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <textarea asp-for="Notes" class="form-control" rows="4"></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-sm btn-primary">Create</button>
                                <a asp-page="./Index" class="btn btn-sm btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
