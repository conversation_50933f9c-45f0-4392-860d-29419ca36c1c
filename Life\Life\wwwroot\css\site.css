:root {
    scroll-behavior: smooth;
}

body {
    font-family: "Open Sans", sans-serif;
    background: #f6f9ff;
    color: #444444;
}

a {
    color: #4154f1;
    text-decoration: none;
}

    a:hover {
        color: #717ff5;
        text-decoration: none;
    }

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Nunito", sans-serif;
}


.ql-container {
    font-family: "Poppins", sans-serif;
    font-size: 14px;
}


.list-group-item h5 {
    color: #012970;
    font-family: "Poppins", sans-serif;
}

.list-group-item a {
    height: 32px;
}

.title-button {
    position: absolute;
    right: 20px;
    top: 15px;
}


form label {
    color: #012970;
    font-family: "Poppins", sans-serif;
}


.reminder {
    background-color: #f6f9ff;
}

.reminder a h5:hover {
    color: #3275ed;
}

.expired-items .list-group-item {
    background-color: #fa1c2e2d;
}

.expired-items .reminder {
    background-color: #a3121e2d;
}

.expired-items .expired-date {
    color: #fa1c2e !important;
}



.required-field {
    font-weight: bold;
}

/* Make ASP.NET validation messages visible with Bootstrap styling */
.invalid-feedback.field-validation-error {
    display: block !important;
}

.invalid-feedback.field-validation-valid {
    display: none !important;
}
/* Style inputs with validation errors */
.input-validation-error {
    border-color: #dc3545 !important;
}

    .input-validation-error:focus {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }