using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.DrivingLicences
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "Name is required")]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Driving Licence Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Start Date is required")]
        [DisplayName("Start Date")]
        public DateOnly StartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Expiry Date is required")]
        [DisplayName("Expiry Date")]
        public DateOnly ExpiryDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Photocard Start Date is required")]
        [DisplayName("Photocard Start Date")]
        public DateOnly PhotocardStartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Photocard Expiry Date is required")]
        [DisplayName("Photocard Expiry Date")]
        public DateOnly PhotocardExpiryDate { get; set; }

        [BindProperty]
        [StringLength(16, ErrorMessage = "Licence Number cannot exceed 16 characters")]
        [DisplayName("Licence Number")]
        public string? LicenceNumber { get; set; }

        [BindProperty]
        [StringLength(2000, ErrorMessage = "Notes cannot exceed 2000 characters")]
        [DisplayName("Notes")]
        public string? Notes { get; set; }

        [BindProperty]
        [DisplayName("Address")]
        public int? AddressId { get; set; }

        public SelectList? Addresses { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Set default dates
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow);
            ExpiryDate = DateOnly.FromDateTime(DateTime.UtcNow.AddYears(10));
            PhotocardStartDate = DateOnly.FromDateTime(DateTime.UtcNow);
            PhotocardExpiryDate = DateOnly.FromDateTime(DateTime.UtcNow.AddYears(10));

            // Load addresses for dropdown
            await LoadAddresses(currentUser.Id);

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that expiry date is after start date
            if (ExpiryDate <= StartDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must be after start date");
            }

            // Validate that expiry date is not before photocard expiry date
            if (ExpiryDate < PhotocardExpiryDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must not be before photocard expiry date");
            }

            // Validate that photocard expiry date is after photocard start date
            if (PhotocardExpiryDate <= PhotocardStartDate)
            {
                ModelState.AddModelError(nameof(PhotocardExpiryDate), "Photocard expiry date must be after photocard start date");
            }


            if (!ModelState.IsValid)
            {
                await LoadAddresses(currentUser.Id);
                return Page();
            }

            // Create the driving licence
            var drivingLicence = new DrivingLicence
            {
                Name = Name,
                StartDate = StartDate,
                ExpiryDate = ExpiryDate,
                PhotocardStartDate = PhotocardStartDate,
                PhotocardExpiryDate = PhotocardExpiryDate,
                LicenceNumber = LicenceNumber,
                Notes = Notes,
                AddressId = AddressId,
                User = currentUser,
                UserId = currentUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.DrivingLicences.Add(drivingLicence);
            await _context.SaveChangesAsync();

            // Create automatic reminder for driving licence expiry (Number = 1)
            var expiryReminder = new Reminder
            {
                Name = $"{Name} - Driving Licence Expiry Date",
                ReminderDate = ExpiryDate,
                User = currentUser,
                UserId = currentUser.Id,
                DrivingLicenceId = drivingLicence.Id,
                IsAutomatic = true,
                Number = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Reminders.Add(expiryReminder);

            // Create automatic reminder for photocard expiry (Number = 2)
            var photocardReminder = new Reminder
            {
                Name = $"{Name} - Driving Licence Photocard Expiry Date",
                ReminderDate = PhotocardExpiryDate,
                User = currentUser,
                UserId = currentUser.Id,
                DrivingLicenceId = drivingLicence.Id,
                IsAutomatic = true,
                Number = 2,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Reminders.Add(photocardReminder);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Driving licence created successfully";

            return RedirectToPage("./Index");
        }

        private async Task LoadAddresses(string userId)
        {
            var addresses = await _context.Addresses
                .AsNoTracking()
                .Where(a => a.UserId == userId)
                .OrderBy(a => a.HouseFlatNumber)
                .ThenBy(a => a.StreetLineOne)
                .Select(a => new { a.Id, Display = $"{a.HouseFlatNumber} {a.StreetLineOne}, {a.City}, {a.Postcode}" })
                .ToListAsync();

            Addresses = new SelectList(addresses, "Id", "Display", AddressId);
        }
    }
}
