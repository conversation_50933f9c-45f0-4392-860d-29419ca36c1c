@page
@model Life.Pages.Passports.DetailsModel
@{
    ViewData["Title"] = "Passport Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">Passport Details</h2>
                    <a asp-page="./Index" class="btn btn-sm btn-outline-primary title-button">
                        <i class="bi bi-list"></i>
                    </a>
                </div>
                <div class="card-body">

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                            <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                                @TempData["SuccessMessage"]
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        </div>
                    }

                    <div class="row mt-3">
                        <div class="col-md-8">
                            <h4 class="card-title">@Model.Name</h4>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Passport Number:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.PassportNumber
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Start Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.StartDate.ToString("dd/MM/yyyy")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Expiry Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="@(Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow) 
                                        ? "text-danger" 
                                        : Model.ExpiryDate == DateOnly.FromDateTime(DateTime.UtcNow) 
                                            ? "text-danger" 
                                            : Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6)) 
                                                ? "text-warning" 
                                                : "text-success")">
                                        @Model.ExpiryDate.ToString("dd/MM/yyyy")
                                        @if (Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow))
                                        {
                                            <span class="badge bg-danger ms-2">Expired</span>
                                        }
                                        else if (Model.ExpiryDate == DateOnly.FromDateTime(DateTime.UtcNow))
                                        {
                                            <span class="badge bg-danger ms-2">Expires Today</span>

                                        }
                                        else if (Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6)))
                                        {
                                            <span class="badge bg-warning ms-2">Expires Soon</span>
                                        }
                                    </span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Cost:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.Cost.HasValue)
                                    {
                                        @:&#163;@Model.Cost.Value.ToString("F2")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Size:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.Size
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Created:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Last Updated:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-bell"></i> Reminder Status
                                    </h6>
                                    @if (Model.HasLinkedReminder)
                                    {
                                        <p class="text-success">
                                            <i class="bi bi-check-circle"></i> 
                                            Automatic reminder is set
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="text-warning">
                                            <i class="bi bi-exclamation-triangle"></i> 
                                            No automatic reminder found
                                        </p>
                                    }
                                </div>
                            </div>

                            <div class="card bg-light mt-3">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-calendar"></i> Time Remaining
                                    </h6>
                                    @{
                                        var today = DateOnly.FromDateTime(DateTime.UtcNow);
                                        var daysUntilExpiry = (Model.ExpiryDate.DayNumber - today.DayNumber);
                                    }
                                    @if (daysUntilExpiry < 0)
                                    {
                                        <p class="text-danger">
                                            Expired @Math.Abs(daysUntilExpiry) days ago
                                        </p>
                                    }
                                    else if (daysUntilExpiry == 0)
                                    {
                                        <p class="text-danger">
                                            Expires today!
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="@(daysUntilExpiry < 180 ? "text-warning" : "text-success")">
                                            @if (daysUntilExpiry == 1)
                                            {
                                                @:1 day remaining
                                            }
                                            else
                                            {
										        @daysUntilExpiry @:days remaining
                                            }
                                        </p>
                                    }
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-3">
                                <a asp-page="./Edit" asp-route-id="@Model.PassportId" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a asp-page="./Delete" asp-route-id="@Model.PassportId" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-trash"></i> Delete
                                </a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
