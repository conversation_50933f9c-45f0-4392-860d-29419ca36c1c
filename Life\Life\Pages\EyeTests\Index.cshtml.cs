using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.EyeTests
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<EyeTest> EyeTests { get; set; } = default!;
        public IList<EyeTest> ExpiredEyeTests { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();
            
            // Get current (non-expired) eye tests - those with NextTestDate >= today or no NextTestDate
            EyeTests = await _context.EyeTests
                .AsNoTracking()
                .Where(e => e.UserId == currentUser.Id)
                .Where(e => e.NextTestDate == null || e.NextTestDate >= DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderBy(e => e.TestDate)
                .ToListAsync();

            // Get expired eye tests - those with NextTestDate < today
            ExpiredEyeTests = await _context.EyeTests
                .AsNoTracking()
                .Where(e => e.UserId == currentUser.Id)
                .Where(e => e.NextTestDate != null && e.NextTestDate < DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderByDescending(e => e.TestDate)
                .ToListAsync();

            return Page();
        }
    }
}
