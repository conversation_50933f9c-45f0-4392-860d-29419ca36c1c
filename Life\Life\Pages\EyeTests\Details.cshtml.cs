using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.EyeTests
{
    public class DetailsModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DetailsModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public int EyeTestId { get; set; }
        public string Provider { get; set; } = string.Empty;
        public DateOnly TestDate { get; set; }
        public DateOnly? NextTestDate { get; set; }
        public decimal? RightSPH { get; set; }
        public decimal? RightCYL { get; set; }
        public decimal? RightAXIS { get; set; }
        public decimal? LeftSPH { get; set; }
        public decimal? LeftCYL { get; set; }
        public decimal? LeftAXIS { get; set; }
        public decimal? Cost { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool HasLinkedReminder { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var eyeTest = await _context.EyeTests
                .AsNoTracking()
                .Where(e => e.Id == id && e.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (eyeTest == null)
            {
                return NotFound();
            }

            EyeTestId = eyeTest.Id;
            Provider = eyeTest.Provider;
            TestDate = eyeTest.TestDate;
            NextTestDate = eyeTest.NextTestDate;
            RightSPH = eyeTest.RightSPH;
            RightCYL = eyeTest.RightCYL;
            RightAXIS = eyeTest.RightAXIS;
            LeftSPH = eyeTest.LeftSPH;
            LeftCYL = eyeTest.LeftCYL;
            LeftAXIS = eyeTest.LeftAXIS;
            Cost = eyeTest.Cost;
            Notes = eyeTest.Notes;
            CreatedAt = eyeTest.CreatedAt;
            UpdatedAt = eyeTest.UpdatedAt;

            // Check if there's a linked automatic reminder
            HasLinkedReminder = await _context.Reminders
                .AnyAsync(r => r.EyeTestId == eyeTest.Id && r.IsAutomatic == true);

            return Page();
        }
    }
}
