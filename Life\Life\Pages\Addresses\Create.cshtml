@page
@model Life.Pages.Addresses.CreateModel
@{
    ViewData["Title"] = "Create Address";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Create Address</h2>
                </div>
                <div class="card-body">
                    <form method="post" class="row g-3 mt-1" autocomplete="off">

                        <div class="row my-3">
                            <small class="text-muted">Required fields are marked in <strong>bold</strong>.</small>
                        </div>

                        <div class="row my-3">
                            <label asp-for="HouseFlatNumber" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="HouseFlatNumber" class="form-control" />
                                <span asp-validation-for="HouseFlatNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="StreetLineOne" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="StreetLineOne" class="form-control" />
                                <span asp-validation-for="StreetLineOne" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="StreetLineTwo" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="StreetLineTwo" class="form-control" />
                                <span asp-validation-for="StreetLineTwo" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="City" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="City" class="form-control" />
                                <span asp-validation-for="City" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Postcode" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="Postcode" class="form-control" />
                                <span asp-validation-for="Postcode" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="MoveInDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="MoveInDate" class="form-control" type="date" />
                                <span asp-validation-for="MoveInDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="MoveOutDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="MoveOutDate" class="form-control" type="date" />
                                <span asp-validation-for="MoveOutDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="PurchaseDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="PurchaseDate" class="form-control" type="date" />
                                <span asp-validation-for="PurchaseDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="PurchasePrice" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="PurchasePrice" class="form-control" type="number" />
                                <span asp-validation-for="PurchasePrice" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="SoldDate" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="SoldDate" class="form-control" type="date" />
                                <span asp-validation-for="SoldDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="SoldPrice" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="SoldPrice" class="form-control" type="number" />
                                <span asp-validation-for="SoldPrice" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Notes" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <textarea asp-for="Notes" class="form-control" rows="4"></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-sm btn-primary">Create</button>
                                <a asp-page="./Index" class="btn btn-sm btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
