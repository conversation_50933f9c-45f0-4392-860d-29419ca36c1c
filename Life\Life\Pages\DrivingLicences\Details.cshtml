@page
@model Life.Pages.DrivingLicences.DetailsModel
@{
    ViewData["Title"] = "Driving Licence Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">Driving Licence Details</h2>
                    <a asp-page="./Index" class="btn btn-sm btn-outline-primary title-button">
                        <i class="bi bi-list"></i>
                    </a>
                </div>
                <div class="card-body">

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                            <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                                @TempData["SuccessMessage"]
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        </div>
                    }

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Name:</strong></div>
                        <div class="col-sm-9">@Model.Name</div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.LicenceNumber))
                    {
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Licence Number:</strong></div>
                            <div class="col-sm-9">@Model.LicenceNumber</div>
                        </div>
                    }

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Start Date:</strong></div>
                        <div class="col-sm-9">@Model.StartDate.ToString("dd/MM/yyyy")</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Expiry Date:</strong></div>
                        <div class="col-sm-9">@Model.ExpiryDate.ToString("dd/MM/yyyy")</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Photocard Start Date:</strong></div>
                        <div class="col-sm-9">@Model.PhotocardStartDate.ToString("dd/MM/yyyy")</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Photocard Expiry Date:</strong></div>
                        <div class="col-sm-9">@Model.PhotocardExpiryDate.ToString("dd/MM/yyyy")</div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.AddressDisplay))
                    {
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Address:</strong></div>
                            <div class="col-sm-9">
                                <a asp-page="/Addresses/Details" asp-route-id="@Model.AddressId" class="link-primary">
                                    @Model.AddressDisplay
                                </a>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Notes:</strong></div>
                            <div class="col-sm-9">@Model.Notes</div>
                        </div>
                    }

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Created:</strong></div>
                        <div class="col-sm-9">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Last Updated:</strong></div>
                        <div class="col-sm-9">@Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>

                    @if (Model.HasLinkedReminders)
                    {
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Automatic Reminders:</strong></div>
                            <div class="col-sm-9">
                                <span class="badge bg-success">Active</span>
                                <small class="text-muted d-block">Automatic reminders are set for expiry dates</small>
                            </div>
                        </div>
                    }

                    <div class="row mt-4">
                        <div class="col-sm-12">
                            <a asp-page="./Edit" asp-route-id="@Model.DrivingLicenceId" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i> Edit
                            </a>
                            <a asp-page="./Delete" asp-route-id="@Model.DrivingLicenceId" class="btn btn-sm btn-danger">
                                <i class="bi bi-trash"></i> Delete
                            </a>
                            <a asp-page="./Index" class="btn btn-sm btn-secondary">
                                <i class="bi bi-list"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
