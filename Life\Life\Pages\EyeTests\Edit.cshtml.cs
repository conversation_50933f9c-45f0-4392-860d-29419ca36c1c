using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.EyeTests
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int EyeTestId { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Provider is required")]
        [StringLength(50, ErrorMessage = "Provider cannot exceed 50 characters")]
        [DisplayName("Provider")]
        public string Provider { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Test Date is required")]
        [DisplayName("Test Date")]
        public DateOnly TestDate { get; set; }

        [BindProperty]
        [DisplayName("Next Test Date")]
        public DateOnly? NextTestDate { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Right SPH must be between -99.99 and 99.99")]
        [DisplayName("Right SPH")]
        public decimal? RightSPH { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Right CYL must be between -99.99 and 99.99")]
        [DisplayName("Right CYL")]
        public decimal? RightCYL { get; set; }

        [BindProperty]
        [Range(0, 180, ErrorMessage = "Right AXIS must be between 0 and 180")]
        [DisplayName("Right AXIS")]
        public decimal? RightAXIS { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Left SPH must be between -99.99 and 99.99")]
        [DisplayName("Left SPH")]
        public decimal? LeftSPH { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Left CYL must be between -99.99 and 99.99")]
        [DisplayName("Left CYL")]
        public decimal? LeftCYL { get; set; }

        [BindProperty]
        [Range(0, 180, ErrorMessage = "Left AXIS must be between 0 and 180")]
        [DisplayName("Left AXIS")]
        public decimal? LeftAXIS { get; set; }

        [BindProperty]
        [Range(0, 9999999999999999.99, ErrorMessage = "Cost must be a valid amount")]
        [DisplayName("Cost")]
        public decimal? Cost { get; set; }

        [BindProperty]
        [StringLength(2000, ErrorMessage = "Notes cannot exceed 2000 characters")]
        [DisplayName("Notes")]
        public string? Notes { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var eyeTest = await _context.EyeTests
                .Where(e => e.Id == id && e.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (eyeTest == null)
            {
                return NotFound();
            }

            EyeTestId = eyeTest.Id;
            Provider = eyeTest.Provider;
            TestDate = eyeTest.TestDate;
            NextTestDate = eyeTest.NextTestDate;
            RightSPH = eyeTest.RightSPH;
            RightCYL = eyeTest.RightCYL;
            RightAXIS = eyeTest.RightAXIS;
            LeftSPH = eyeTest.LeftSPH;
            LeftCYL = eyeTest.LeftCYL;
            LeftAXIS = eyeTest.LeftAXIS;
            Cost = eyeTest.Cost;
            Notes = eyeTest.Notes;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that next test date is after test date if provided
            if (NextTestDate.HasValue && NextTestDate <= TestDate)
            {
                ModelState.AddModelError(nameof(NextTestDate), "Next test date must be after test date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Get the existing eye test
            var existingEyeTest = await _context.EyeTests
                .Where(e => e.Id == EyeTestId && e.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (existingEyeTest == null)
            {
                return NotFound();
            }

            // Store the original values to check if they changed
            var originalNextTestDate = existingEyeTest.NextTestDate;
            var originalProvider = existingEyeTest.Provider;

            // Update the eye test fields
            existingEyeTest.Provider = Provider;
            existingEyeTest.TestDate = TestDate;
            existingEyeTest.NextTestDate = NextTestDate;
            existingEyeTest.RightSPH = RightSPH;
            existingEyeTest.RightCYL = RightCYL;
            existingEyeTest.RightAXIS = RightAXIS;
            existingEyeTest.LeftSPH = LeftSPH;
            existingEyeTest.LeftCYL = LeftCYL;
            existingEyeTest.LeftAXIS = LeftAXIS;
            existingEyeTest.Cost = Cost;
            existingEyeTest.Notes = Notes;
            existingEyeTest.UpdatedAt = DateTime.UtcNow;

            // Handle reminder management based on NextTestDate changes
            var linkedReminder = await _context.Reminders
                .Where(r => r.EyeTestId == EyeTestId && r.IsAutomatic == true)
                .FirstOrDefaultAsync();

            if (originalNextTestDate != NextTestDate || originalProvider != Provider)
            {
                if (NextTestDate.HasValue)
                {
                    if (linkedReminder != null)
                    {
                        // Update existing reminder
                        linkedReminder.Name = $"{Provider} - Eye Test Due";
                        linkedReminder.ReminderDate = NextTestDate.Value;
                        linkedReminder.UpdatedAt = DateTime.UtcNow;
                    }
                    else
                    {
                        // Create new reminder
                        var newReminder = new Reminder
                        {
                            Name = $"{Provider} - Eye Test Due",
                            ReminderDate = NextTestDate.Value,
                            User = currentUser,
                            UserId = currentUser.Id,
                            EyeTestId = EyeTestId,
                            IsAutomatic = true,
                            Number = 1,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };
                        _context.Reminders.Add(newReminder);
                    }
                }
                else
                {
                    // NextTestDate is null, delete existing reminder if it exists
                    if (linkedReminder != null)
                    {
                        _context.Reminders.Remove(linkedReminder);
                    }
                }
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EyeTestExists(EyeTestId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "Eye test updated successfully";

            return RedirectToPage("./Details", new { id = EyeTestId });
        }

        private bool EyeTestExists(int id)
        {
            return _context.EyeTests.Any(e => e.Id == id);
        }
    }
}
