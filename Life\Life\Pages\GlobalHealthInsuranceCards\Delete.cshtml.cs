using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.GlobalHealthInsuranceCards
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int GlobalHealthInsuranceCardId { get; set; }

        public string Name { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var globalHealthInsuranceCard = await _context.GlobalHealthInsuranceCards
                .Where(g => g.Id == id && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (globalHealthInsuranceCard == null)
            {
                return NotFound();
            }

            GlobalHealthInsuranceCardId = globalHealthInsuranceCard.Id;
            Name = globalHealthInsuranceCard.Name;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var globalHealthInsuranceCard = await _context.GlobalHealthInsuranceCards
                .Where(g => g.Id == id && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (globalHealthInsuranceCard == null)
            {
                return NotFound();
            }

            // Delete the linked automatic reminder first
            var linkedReminder = await _context.Reminders
                .FirstOrDefaultAsync(r => r.GlobalHealthInsuranceCardId == globalHealthInsuranceCard.Id && r.IsAutomatic == true);

            if (linkedReminder != null)
            {
                _context.Reminders.Remove(linkedReminder);
            }

            // Delete the global health insurance card
            _context.GlobalHealthInsuranceCards.Remove(globalHealthInsuranceCard);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "GHIC deleted successfully";

            return RedirectToPage("./Index");
        }
    }
}
