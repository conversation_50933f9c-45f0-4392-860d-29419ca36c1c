@page
@model Life.Pages.Addresses.DetailsModel
@{
    ViewData["Title"] = "Address Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Address Details</h2>
                    <a asp-page="./Index" class="btn btn-sm btn-outline-primary title-button">
                        <i class="bi bi-list"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                <div class="card-body">
                    <div class="row mt-3">
                        <div class="col-md-8">
                            <h4 class="card-title">
                                @Model.HouseFlatNumber @Model.StreetLineOne
                                @if (!string.IsNullOrEmpty(Model.StreetLineTwo))
                                {
                                    <br />@Model.StreetLineTwo
                                }
                                <br />@Model.City
                                <br />@Model.Postcode
                            </h4>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Move In Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.MoveInDate.HasValue)
                                    {
                                        @Model.MoveInDate.Value.ToString("dd/MM/yyyy")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Move Out Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.MoveOutDate.HasValue)
                                    {
                                        @Model.MoveOutDate.Value.ToString("dd/MM/yyyy")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Purchase Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.PurchaseDate.HasValue)
                                    {
                                        @Model.PurchaseDate.Value.ToString("dd/MM/yyyy")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Purchase Price:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.PurchasePrice.HasValue)
                                    {
                                        @:&#163;@Model.PurchasePrice.Value.ToString("F2")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Sold Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.SoldDate.HasValue)
                                    {
                                        @Model.SoldDate.Value.ToString("dd/MM/yyyy")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Sold Price:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (Model.SoldPrice.HasValue)
                                    {
                                        @:&#163;@Model.SoldPrice.Value.ToString("F2")
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Notes:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (!string.IsNullOrWhiteSpace(Model.Notes))
                                    {
                                        <div style="white-space: pre-wrap;">@Model.Notes</div>
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Created:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Last Updated:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Linked Entities Section -->
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-link-45deg"></i> Linked Entities
                                    </h6>
                                    
                                    <!-- Vehicles -->
                                    @if (Model.LinkedVehicles.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Vehicles (@Model.LinkedVehicles.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var vehicle in Model.LinkedVehicles)
                                                {
                                                    <li><small>@vehicle.Make @vehicle.Model (@vehicle.Registration)</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- General Items -->
                                    @if (Model.LinkedGeneralItems.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>General Items (@Model.LinkedGeneralItems.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var item in Model.LinkedGeneralItems)
                                                {
                                                    <li><small>@item.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Driving Licences -->
                                    @if (Model.LinkedDrivingLicences.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Driving Licences (@Model.LinkedDrivingLicences.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var licence in Model.LinkedDrivingLicences)
                                                {
                                                    <li><small>@licence.LicenceNumber</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- GP Practices -->
                                    @if (Model.LinkedGpPractices.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>GP Practices (@Model.LinkedGpPractices.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var gp in Model.LinkedGpPractices)
                                                {
                                                    <li><small>@gp.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Dentists -->
                                    @if (Model.LinkedDentists.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Dentists (@Model.LinkedDentists.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var dentist in Model.LinkedDentists)
                                                {
                                                    <li><small>@dentist.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Current Accounts -->
                                    @if (Model.LinkedCurrentAccounts.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Current Accounts (@Model.LinkedCurrentAccounts.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var account in Model.LinkedCurrentAccounts)
                                                {
                                                    <li><small>@account.Provider - @account.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Credit Cards -->
                                    @if (Model.LinkedCreditCards.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Credit Cards (@Model.LinkedCreditCards.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var card in Model.LinkedCreditCards)
                                                {
                                                    <li><small>@card.Provider - @card.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Savings Accounts -->
                                    @if (Model.LinkedSavingsAccounts.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Savings Accounts (@Model.LinkedSavingsAccounts.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var account in Model.LinkedSavingsAccounts)
                                                {
                                                    <li><small>@account.Provider - @account.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Loans -->
                                    @if (Model.LinkedLoans.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Loans (@Model.LinkedLoans.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var loan in Model.LinkedLoans)
                                                {
                                                    <li><small>@loan.Provider - @loan.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Mortgages -->
                                    @if (Model.LinkedMortgages.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Mortgages (@Model.LinkedMortgages.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var mortgage in Model.LinkedMortgages)
                                                {
                                                    <li><small>@mortgage.Provider - @mortgage.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Pensions -->
                                    @if (Model.LinkedPensions.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Pensions (@Model.LinkedPensions.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var pension in Model.LinkedPensions)
                                                {
                                                    <li><small>@pension.Provider - @pension.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Life Insurance Policies -->
                                    @if (Model.LinkedLifeInsurancePolicies.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Life Insurance (@Model.LinkedLifeInsurancePolicies.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var policy in Model.LinkedLifeInsurancePolicies)
                                                {
                                                    <li><small>@policy.Provider - @policy.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Travel Insurance Policies -->
                                    @if (Model.LinkedTravelInsurancePolicies.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Travel Insurance (@Model.LinkedTravelInsurancePolicies.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var policy in Model.LinkedTravelInsurancePolicies)
                                                {
                                                    <li><small>@policy.Provider - @policy.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Gadget Insurance Policies -->
                                    @if (Model.LinkedGadgetInsurancePolicies.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Gadget Insurance (@Model.LinkedGadgetInsurancePolicies.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var policy in Model.LinkedGadgetInsurancePolicies)
                                                {
                                                    <li><small>@policy.Provider - @policy.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Vehicle Insurance Policies -->
                                    @if (Model.LinkedVehicleInsurancePolicies.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Vehicle Insurance (@Model.LinkedVehicleInsurancePolicies.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var policy in Model.LinkedVehicleInsurancePolicies)
                                                {
                                                    <li><small>@policy.Provider - @policy.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Address Insurance Policies -->
                                    @if (Model.LinkedAddressInsurancePolicies.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Home Insurance (@Model.LinkedAddressInsurancePolicies.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var policy in Model.LinkedAddressInsurancePolicies)
                                                {
                                                    <li><small>@policy.Provider - @policy.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Vehicle Finance Agreements -->
                                    @if (Model.LinkedVehicleFinanceAgreements.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Vehicle Finance (@Model.LinkedVehicleFinanceAgreements.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var agreement in Model.LinkedVehicleFinanceAgreements)
                                                {
                                                    <li><small>@agreement.Provider - @agreement.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Vehicle Breakdown Policies -->
                                    @if (Model.LinkedVehicleBreakdownPolicies.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Vehicle Breakdown (@Model.LinkedVehicleBreakdownPolicies.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var policy in Model.LinkedVehicleBreakdownPolicies)
                                                {
                                                    <li><small>@policy.Provider - @policy.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Tenancies -->
                                    @if (Model.LinkedTenancies.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Tenancies (@Model.LinkedTenancies.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var tenancy in Model.LinkedTenancies)
                                                {
                                                    <li><small>@tenancy.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    <!-- Utility Bills -->
                                    @if (Model.LinkedUtilityBills.Any())
                                    {
                                        <div class="mb-3">
                                            <strong>Utility Bills (@Model.LinkedUtilityBills.Count)</strong>
                                            <ul class="list-unstyled ms-3">
                                                @foreach (var bill in Model.LinkedUtilityBills)
                                                {
                                                    <li><small>@bill.Provider - @bill.Name</small></li>
                                                }
                                            </ul>
                                        </div>
                                    }

                                    @{
                                        var hasAnyLinkedEntities = Model.LinkedVehicles.Any() || Model.LinkedGeneralItems.Any() ||
                                                                   Model.LinkedDrivingLicences.Any() || Model.LinkedGpPractices.Any() ||
                                                                   Model.LinkedDentists.Any() || Model.LinkedCurrentAccounts.Any() ||
                                                                   Model.LinkedCreditCards.Any() || Model.LinkedSavingsAccounts.Any() ||
                                                                   Model.LinkedLoans.Any() || Model.LinkedMortgages.Any() ||
                                                                   Model.LinkedPensions.Any() || Model.LinkedLifeInsurancePolicies.Any() ||
                                                                   Model.LinkedTravelInsurancePolicies.Any() || Model.LinkedGadgetInsurancePolicies.Any() ||
                                                                   Model.LinkedVehicleInsurancePolicies.Any() || Model.LinkedAddressInsurancePolicies.Any() ||
                                                                   Model.LinkedVehicleFinanceAgreements.Any() || Model.LinkedVehicleBreakdownPolicies.Any() ||
                                                                   Model.LinkedTenancies.Any() || Model.LinkedUtilityBills.Any();
                                    }

                                    @if (!hasAnyLinkedEntities)
                                    {
                                        <p class="text-muted">
                                            <small>No entities are currently linked to this address.</small>
                                        </p>
                                    }
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-3">
                                <a asp-page="./Edit" asp-route-id="@Model.AddressId" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a asp-page="./Delete" asp-route-id="@Model.AddressId" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-trash"></i> Delete
                                </a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
