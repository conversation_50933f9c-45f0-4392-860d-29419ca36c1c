using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.EyeTests
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "Provider is required")]
        [StringLength(50, ErrorMessage = "Provider cannot exceed 50 characters")]
        [DisplayName("Provider")]
        public string Provider { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Test Date is required")]
        [DisplayName("Test Date")]
        public DateOnly TestDate { get; set; }

        [BindProperty]
        [DisplayName("Next Test Date")]
        public DateOnly? NextTestDate { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Right SPH must be between -99.99 and 99.99")]
        [DisplayName("Right SPH")]
        public decimal? RightSPH { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Right CYL must be between -99.99 and 99.99")]
        [DisplayName("Right CYL")]
        public decimal? RightCYL { get; set; }

        [BindProperty]
        [Range(0, 180, ErrorMessage = "Right AXIS must be between 0 and 180")]
        [DisplayName("Right AXIS")]
        public decimal? RightAXIS { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Left SPH must be between -99.99 and 99.99")]
        [DisplayName("Left SPH")]
        public decimal? LeftSPH { get; set; }

        [BindProperty]
        [Range(-99.99, 99.99, ErrorMessage = "Left CYL must be between -99.99 and 99.99")]
        [DisplayName("Left CYL")]
        public decimal? LeftCYL { get; set; }

        [BindProperty]
        [Range(0, 180, ErrorMessage = "Left AXIS must be between 0 and 180")]
        [DisplayName("Left AXIS")]
        public decimal? LeftAXIS { get; set; }

        [BindProperty]
        [Range(0, 9999999999999999.99, ErrorMessage = "Cost must be a valid amount")]
        [DisplayName("Cost")]
        public decimal? Cost { get; set; }

        [BindProperty]
        [StringLength(2000, ErrorMessage = "Notes cannot exceed 2000 characters")]
        [DisplayName("Notes")]
        public string? Notes { get; set; }

        public IActionResult OnGet()
        {
            // Set default test date to today
            TestDate = DateOnly.FromDateTime(DateTime.UtcNow);

            return Page();
        }   

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that next test date is after test date if provided
            if (NextTestDate.HasValue && NextTestDate <= TestDate)
            {
                ModelState.AddModelError(nameof(NextTestDate), "Next test date must be after test date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Create the eye test
            var eyeTest = new EyeTest
            {
                Provider = Provider,
                TestDate = TestDate,
                NextTestDate = NextTestDate,
                RightSPH = RightSPH,
                RightCYL = RightCYL,
                RightAXIS = RightAXIS,
                LeftSPH = LeftSPH,
                LeftCYL = LeftCYL,
                LeftAXIS = LeftAXIS,
                Cost = Cost,
                Notes = Notes,
                User = currentUser,
                UserId = currentUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.EyeTests.Add(eyeTest);
            await _context.SaveChangesAsync();

            // Create automatic reminder for next test date if provided
            if (NextTestDate.HasValue)
            {
                var reminder = new Reminder
                {
                    Name = $"{Provider} - Eye Test Due",
                    ReminderDate = NextTestDate.Value,
                    User = currentUser,
                    UserId = currentUser.Id,
                    EyeTestId = eyeTest.Id,
                    IsAutomatic = true,
                    Number = 1,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Reminders.Add(reminder);
                await _context.SaveChangesAsync();
            }

            TempData["SuccessMessage"] = "Eye test created successfully";

            return RedirectToPage("./Index");
        }
    }
}
