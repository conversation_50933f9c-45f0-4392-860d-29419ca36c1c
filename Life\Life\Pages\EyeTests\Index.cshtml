@page
@model Life.Pages.EyeTests.IndexModel
@{
    ViewData["Title"] = "Eye Tests";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Eye Tests</h2>
                    <a asp-page="./Create" class="btn btn-sm btn-outline-success title-button">
                        <i class="bi bi-plus-square"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                @if (Model.EyeTests.Any())
                {
                    <div class="card-body">
                        <h5 class="card-title">Eye Tests</h5>

                        <div class="list-group">
                            @foreach (var eyeTest in Model.EyeTests)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@eyeTest.Provider - @eyeTest.TestDate.ToString("dd/MM/yyyy")</h5>
                                            @if (eyeTest.NextTestDate.HasValue)
                                            {
                                                <h6>Next Test: @eyeTest.NextTestDate.Value.ToString("dd/MM/yyyy")</h6>
                                            }
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@eyeTest.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@eyeTest.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                } 
                else
                {
                    <div class="card-body">
                        <h5 class="card-title">Eye Tests</h5>
                        <p>There are no eye tests</p>
                    </div>
                }

                @if (Model.ExpiredEyeTests.Any())
                {
                    <hr class="mt-5" />
                    <div class="card-body expired-items">
                        <h5 class="card-title">Overdue Eye Tests</h5>

                        <div class="list-group">
                            @foreach (var eyeTest in Model.ExpiredEyeTests)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@eyeTest.Provider -  @eyeTest.TestDate.ToString("dd/MM/yyyy")</h5>
                                            @if (eyeTest.NextTestDate.HasValue)
                                            {
                                                <h6 class="expired-date">Next Test Due: @eyeTest.NextTestDate.Value.ToString("dd/MM/yyyy")</h6>
                                            }
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@eyeTest.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@eyeTest.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
