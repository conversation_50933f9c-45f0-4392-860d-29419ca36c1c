using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.GlobalHealthInsuranceCards
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(50, ErrorMessage = "Personal ID Number cannot exceed 50 characters")]
        [DisplayName("Personal ID Number")]
        public string PersonalIdNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(50, ErrorMessage = "Card ID Number cannot exceed 50 characters")]
        [DisplayName("Card ID Number")]
        public string CardIdNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Start Date is required")]
        [DisplayName("Start Date")]
        public DateOnly StartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Expiry Date is required")]
        [DisplayName("Expiry Date")]
        public DateOnly ExpiryDate { get; set; }

        public IActionResult OnGet()
        {
            // Set default dates
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow);
            ExpiryDate = DateOnly.FromDateTime(DateTime.UtcNow.AddYears(5));

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that expiry date is after start date
            if (ExpiryDate <= StartDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must be after start date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Create the global health insurance card
            var globalHealthInsuranceCard = new GlobalHealthInsuranceCard
            {
                Name = Name,
                PersonalIdNumber = PersonalIdNumber,
                CardIdNumber = CardIdNumber,
                StartDate = StartDate,
                ExpiryDate = ExpiryDate,
                User = currentUser,
                UserId = currentUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.GlobalHealthInsuranceCards.Add(globalHealthInsuranceCard);
            await _context.SaveChangesAsync();

            // Create automatic reminder for expiry date
            var reminder = new Reminder
            {
                Name = $"{Name} - GHIC Expiry Date",
                ReminderDate = ExpiryDate,
                User = currentUser,
                UserId = currentUser.Id,
                GlobalHealthInsuranceCardId = globalHealthInsuranceCard.Id,
                IsAutomatic = true,
                Number = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Reminders.Add(reminder);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "GHIC created successfully";

            return RedirectToPage("./Index");
        }
    }
}
