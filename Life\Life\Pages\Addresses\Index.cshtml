@page
@model Life.Pages.Addresses.IndexModel
@{
    ViewData["Title"] = "Addresses";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Addresses</h2>
                    <a asp-page="./Create" class="btn btn-sm btn-outline-success title-button">
                        <i class="bi bi-plus-square"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                @if (Model.Addresses.Any())
                {
                    <div class="card-body">
                        <h5 class="card-title">Address History</h5>

                        <div class="list-group">
                            @foreach (var address in Model.Addresses)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@address.HouseFlatNumber @address.StreetLineOne, 
                                                @if (!string.IsNullOrEmpty(address.StreetLineTwo))
                                                {
                                                    @address.StreetLineTwo@:,
												}
                                                @address.City, @address.Postcode</h5>
                                            @if (address.MoveInDate.HasValue)
                                            {
                                                <h6>Moved In: @address.MoveInDate.Value.ToString("dd/MM/yyyy")</h6>
                                            }
                                            @if (address.MoveOutDate.HasValue)
                                            {
                                                <h6>Moved Out: @address.MoveOutDate.Value.ToString("dd/MM/yyyy")</h6>
                                            }
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@address.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@address.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                } 
                else
                {
                    <div class="card-body">
                        <h5 class="card-title">Address History</h5>
                        <p>There are no addresses</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
