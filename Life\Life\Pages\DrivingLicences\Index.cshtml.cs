using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.DrivingLicences
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<DrivingLicence> DrivingLicences { get; set; } = default!;
        public IList<DrivingLicence> ExpiredDrivingLicences { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();
            
            // Get current (non-expired) driving licences
            DrivingLicences = await _context.DrivingLicences
                .AsNoTracking()
                .Where(d => d.UserId == currentUser.Id)
                .Where(d => d.PhotocardExpiryDate >= DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderBy(d => d.PhotocardExpiryDate)
                .ToListAsync();

            // Get expired driving licences
            ExpiredDrivingLicences = await _context.DrivingLicences
                .AsNoTracking()
                .Where(d => d.UserId == currentUser.Id)
                .Where(d => d.PhotocardExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderByDescending(d => d.PhotocardExpiryDate)
                .ToListAsync();

            return Page();
        }
    }
}
