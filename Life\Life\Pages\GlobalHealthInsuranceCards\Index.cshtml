@page
@model Life.Pages.GlobalHealthInsuranceCards.IndexModel
@{
    ViewData["Title"] = "Global Health Insurance Cards";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Global Health Insurance Cards</h2>
                    <a asp-page="./Create" class="btn btn-sm btn-outline-success title-button">
                        <i class="bi bi-plus-square"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                @if (Model.GlobalHealthInsuranceCards.Any())
                {
                    <div class="card-body">
                        <h5 class="card-title">Current GHICs</h5>

                        <div class="list-group">
                            @foreach (var card in Model.GlobalHealthInsuranceCards)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@card.Name</h5>
                                            <h6>Expires: @card.ExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@card.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@card.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
                else
                {
                    <div class="card-body">
                        <h5 class="card-title">Current GHICs</h5>
                        <p>There are no current GHICs</p>
                    </div>
                }

                @if (Model.ExpiredGlobalHealthInsuranceCards.Any())
                {
                    <hr class="mt-5" />
                    <div class="card-body expired-items">
                        <h5 class="card-title">Expired GHICs</h5>

                        <div class="list-group">
                            @foreach (var card in Model.ExpiredGlobalHealthInsuranceCards)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@card.Name</h5>
                                            <h6 class="expired-date">Expired: @card.ExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@card.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@card.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>