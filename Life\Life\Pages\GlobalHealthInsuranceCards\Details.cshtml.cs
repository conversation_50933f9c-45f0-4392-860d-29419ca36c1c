using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.GlobalHealthInsuranceCards
{
    public class DetailsModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DetailsModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public int GlobalHealthInsuranceCardId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string PersonalIdNumber { get; set; } = string.Empty;
        public string CardIdNumber { get; set; } = string.Empty;
        public DateOnly StartDate { get; set; }
        public DateOnly ExpiryDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool HasLinkedReminder { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var globalHealthInsuranceCard = await _context.GlobalHealthInsuranceCards
                .AsNoTracking()
                .Where(g => g.Id == id && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (globalHealthInsuranceCard == null)
            {
                return NotFound();
            }

            GlobalHealthInsuranceCardId = globalHealthInsuranceCard.Id;
            Name = globalHealthInsuranceCard.Name;
            PersonalIdNumber = globalHealthInsuranceCard.PersonalIdNumber;
            CardIdNumber = globalHealthInsuranceCard.CardIdNumber;
            StartDate = globalHealthInsuranceCard.StartDate;
            ExpiryDate = globalHealthInsuranceCard.ExpiryDate;
            CreatedAt = globalHealthInsuranceCard.CreatedAt;
            UpdatedAt = globalHealthInsuranceCard.UpdatedAt;

            // Check if there's a linked automatic reminder
            HasLinkedReminder = await _context.Reminders
                .AnyAsync(r => r.GlobalHealthInsuranceCardId == globalHealthInsuranceCard.Id && r.IsAutomatic == true);

            return Page();
        }
    }
}
