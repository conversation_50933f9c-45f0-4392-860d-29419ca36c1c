using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.Addresses
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "House/Flat Number is required")]
        [StringLength(20, ErrorMessage = "House/Flat Number cannot exceed 20 characters")]
        [DisplayName("House/Flat Number")]
        public string HouseFlatNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Street Line One is required")]
        [StringLength(100, ErrorMessage = "Street Line One cannot exceed 100 characters")]
        [DisplayName("Street Line One")]
        public string StreetLineOne { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(100, ErrorMessage = "Street Line Two cannot exceed 100 characters")]
        [DisplayName("Street Line Two")]
        public string? StreetLineTwo { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "City is required")]
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        [DisplayName("City")]
        public string City { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Postcode is required")]
        [StringLength(20, ErrorMessage = "Postcode cannot exceed 20 characters")]
        [DisplayName("Postcode")]
        public string Postcode { get; set; } = string.Empty;

        [BindProperty]
        [DisplayName("Move In Date")]
        public DateOnly? MoveInDate { get; set; }

        [BindProperty]
        [DisplayName("Move Out Date")]
        public DateOnly? MoveOutDate { get; set; }

        [BindProperty]
        [DisplayName("Purchase Date")]
        public DateOnly? PurchaseDate { get; set; }

        [BindProperty]
        [Range(0, 9999999999999999.99, ErrorMessage = "Purchase Price must be a valid amount")]
        [DisplayName("Purchase Price")]
        public decimal? PurchasePrice { get; set; }

        [BindProperty]
        [DisplayName("Sold Date")]
        public DateOnly? SoldDate { get; set; }

        [BindProperty]
        [Range(0, 9999999999999999.99, ErrorMessage = "Sold Price must be a valid amount")]
        [DisplayName("Sold Price")]
        public decimal? SoldPrice { get; set; }

        [BindProperty]
        [StringLength(2000, ErrorMessage = "Notes cannot exceed 2000 characters")]
        [DisplayName("Notes")]
        public string? Notes { get; set; }

        public IActionResult OnGet()
        {
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate date logic
            if (MoveOutDate.HasValue && MoveInDate.HasValue && MoveOutDate <= MoveInDate)
            {
                ModelState.AddModelError(nameof(MoveOutDate), "Move out date must be after move in date");
            }

            if (SoldDate.HasValue && PurchaseDate.HasValue && SoldDate <= PurchaseDate)
            {
                ModelState.AddModelError(nameof(SoldDate), "Sold date must be after purchase date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Create the address
            var address = new Address
            {
                HouseFlatNumber = HouseFlatNumber,
                StreetLineOne = StreetLineOne,
                StreetLineTwo = StreetLineTwo,
                City = City,
                Postcode = Postcode,
                MoveInDate = MoveInDate,
                MoveOutDate = MoveOutDate,
                PurchaseDate = PurchaseDate,
                PurchasePrice = PurchasePrice,
                SoldDate = SoldDate,
                SoldPrice = SoldPrice,
                Notes = Notes,
                User = currentUser,
                UserId = currentUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Addresses.Add(address);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Address created successfully";

            return RedirectToPage("./Index");
        }
    }
}
