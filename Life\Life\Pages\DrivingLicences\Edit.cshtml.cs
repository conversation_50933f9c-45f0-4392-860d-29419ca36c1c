using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.DrivingLicences
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int DrivingLicenceId { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Name is required")]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Driving Licence Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Start Date is required")]
        [DisplayName("Start Date")]
        public DateOnly StartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Expiry Date is required")]
        [DisplayName("Expiry Date")]
        public DateOnly ExpiryDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Photocard Start Date is required")]
        [DisplayName("Photocard Start Date")]
        public DateOnly PhotocardStartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Photocard Expiry Date is required")]
        [DisplayName("Photocard Expiry Date")]
        public DateOnly PhotocardExpiryDate { get; set; }

        [BindProperty]
        [StringLength(16, ErrorMessage = "Licence Number cannot exceed 16 characters")]
        [DisplayName("Licence Number")]
        public string? LicenceNumber { get; set; }

        [BindProperty]
        [StringLength(2000, ErrorMessage = "Notes cannot exceed 2000 characters")]
        [DisplayName("Notes")]
        public string? Notes { get; set; }

        [BindProperty]
        [DisplayName("Address")]
        public int? AddressId { get; set; }

        public SelectList? Addresses { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var drivingLicence = await _context.DrivingLicences
                .Where(d => d.Id == id && d.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (drivingLicence == null)
            {
                return NotFound();
            }

            DrivingLicenceId = drivingLicence.Id;
            Name = drivingLicence.Name;
            StartDate = drivingLicence.StartDate;
            ExpiryDate = drivingLicence.ExpiryDate;
            PhotocardStartDate = drivingLicence.PhotocardStartDate;
            PhotocardExpiryDate = drivingLicence.PhotocardExpiryDate;
            LicenceNumber = drivingLicence.LicenceNumber;
            Notes = drivingLicence.Notes;
            AddressId = drivingLicence.AddressId;

            // Load addresses for dropdown
            await LoadAddresses(currentUser.Id);

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that expiry date is after start date
            if (ExpiryDate <= StartDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must be after start date");
            }

            // Validate that expiry date is not before photocard expiry date
            if (ExpiryDate < PhotocardExpiryDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must not be before photocard expiry date");
            }

            // Validate that photocard expiry date is after photocard start date
            if (PhotocardExpiryDate <= PhotocardStartDate)
            {
                ModelState.AddModelError(nameof(PhotocardExpiryDate), "Photocard expiry date must be after photocard start date");
            }

            if (!ModelState.IsValid)
            {
                await LoadAddresses(currentUser.Id);
                return Page();
            }

            // Get the existing driving licence
            var existingDrivingLicence = await _context.DrivingLicences
                .Where(d => d.Id == DrivingLicenceId && d.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (existingDrivingLicence == null)
            {
                return NotFound();
            }

            // Store the original values to check if they changed
            var originalExpiryDate = existingDrivingLicence.ExpiryDate;
            var originalPhotocardExpiryDate = existingDrivingLicence.PhotocardExpiryDate;
            var originalName = existingDrivingLicence.Name;

            // Update the driving licence fields
            existingDrivingLicence.Name = Name;
            existingDrivingLicence.StartDate = StartDate;
            existingDrivingLicence.ExpiryDate = ExpiryDate;
            existingDrivingLicence.PhotocardStartDate = PhotocardStartDate;
            existingDrivingLicence.PhotocardExpiryDate = PhotocardExpiryDate;
            existingDrivingLicence.LicenceNumber = LicenceNumber;
            existingDrivingLicence.Notes = Notes;
            existingDrivingLicence.AddressId = AddressId;
            existingDrivingLicence.UpdatedAt = DateTime.UtcNow;

            // If the expiry date or name changed, update the linked reminder (Number = 1)
            if (originalExpiryDate != ExpiryDate || originalName != Name)
            {
                var linkedExpiryReminder = await _context.Reminders
                    .Where(r => r.DrivingLicenceId == DrivingLicenceId && r.IsAutomatic == true && r.Number == 1)
                    .FirstOrDefaultAsync();

                if (linkedExpiryReminder != null)
                {
                    linkedExpiryReminder.Name = $"{Name} - Driving Licence Expiry Date";
                    linkedExpiryReminder.ReminderDate = ExpiryDate;
                    linkedExpiryReminder.UpdatedAt = DateTime.UtcNow;
                }
            }

            // If the photocard expiry date or name changed, update the linked reminder (Number = 2)
            if (originalPhotocardExpiryDate != PhotocardExpiryDate || originalName != Name)
            {
                var linkedPhotocardReminder = await _context.Reminders
                    .Where(r => r.DrivingLicenceId == DrivingLicenceId && r.IsAutomatic == true && r.Number == 2)
                    .FirstOrDefaultAsync();

                if (linkedPhotocardReminder != null)
                {
                    linkedPhotocardReminder.Name = $"{Name} - Driving Licence Photocard Expiry Date";
                    linkedPhotocardReminder.ReminderDate = PhotocardExpiryDate;
                    linkedPhotocardReminder.UpdatedAt = DateTime.UtcNow;
                }
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DrivingLicenceExists(DrivingLicenceId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "Driving licence updated successfully";

            return RedirectToPage("./Details", new { id = DrivingLicenceId });
        }

        private async Task LoadAddresses(string userId)
        {
            var addresses = await _context.Addresses
                .AsNoTracking()
                .Where(a => a.UserId == userId)
                .OrderBy(a => a.HouseFlatNumber)
                .ThenBy(a => a.StreetLineOne)
                .Select(a => new { a.Id, Display = $"{a.HouseFlatNumber} {a.StreetLineOne}, {a.City}, {a.Postcode}" })
                .ToListAsync();

            Addresses = new SelectList(addresses, "Id", "Display", AddressId);
        }

        private bool DrivingLicenceExists(int id)
        {
            return _context.DrivingLicences.Any(e => e.Id == id);
        }
    }
}
