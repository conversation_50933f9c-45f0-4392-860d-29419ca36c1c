using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Addresses
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int AddressId { get; set; }

        public string AddressDisplay { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var address = await _context.Addresses
                .Where(a => a.Id == id && a.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (address == null)
            {
                return NotFound();
            }

            AddressId = address.Id;
            AddressDisplay = $"{address.HouseFlatNumber} {address.StreetLineOne}, {address.City}, {address.Postcode}";

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var address = await _context.Addresses
                .Where(a => a.Id == id && a.UserId == currentUser.Id)
                .Include(a => a.GeneralItems)
                .Include(a => a.DrivingLicences)
                .Include(a => a.GpPractices)
                .Include(a => a.Dentists)
                .Include(a => a.LifeInsurancePolicies)
                .Include(a => a.Vehicles)
                .Include(a => a.CurrentAccounts)
                .Include(a => a.CreditCards)
                .Include(a => a.SavingsAccounts)
                .Include(a => a.Loans)
                .Include(a => a.Pensions)
                .Include(a => a.TravelInsurancePolicies)
                .Include(a => a.GadgetInsurancePolicies)
                .Include(a => a.VehicleInsurancePolicies)
                .Include(a => a.VehicleBreakdownPolicies)
                .Include(a => a.VehicleFinanceAgreements)
                .Include(a => a.Mortgages)
                .Include(a => a.UtilityBills)
                .Include(a => a.Tenancies)
                .Include(a => a.AddressInsurancePolicies)
                .FirstOrDefaultAsync();

            if (address == null)
            {
                return NotFound();
            }

            address.GeneralItems.Clear();
            address.DrivingLicences.Clear();
            address.GpPractices.Clear();
            address.Dentists.Clear();
            address.LifeInsurancePolicies.Clear();
            address.Vehicles.Clear();
            address.CurrentAccounts.Clear();
            address.CreditCards.Clear();
            address.SavingsAccounts.Clear();
            address.Loans.Clear();
            address.Pensions.Clear();
            address.TravelInsurancePolicies.Clear();
            address.GadgetInsurancePolicies.Clear();
            address.VehicleInsurancePolicies.Clear();
            address.VehicleBreakdownPolicies.Clear();
            address.VehicleFinanceAgreements.Clear();

            foreach (var mortgage in address.Mortgages)
            {
                _context.Mortgages.Remove(mortgage);
            }

            foreach (var utilityBill in address.UtilityBills)
            {
                _context.UtilityBills.Remove(utilityBill);
            }

            foreach (var tenancy in address.Tenancies)
            {
                _context.Tenancies.Remove(tenancy);
            }

            foreach (var insurancePolicy in address.AddressInsurancePolicies)
            {
                _context.AddressInsurancePolicies.Remove(insurancePolicy);
            }

            // Delete the address
            _context.Addresses.Remove(address);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Address deleted successfully";

            return RedirectToPage("./Index");
        }
    }
}
