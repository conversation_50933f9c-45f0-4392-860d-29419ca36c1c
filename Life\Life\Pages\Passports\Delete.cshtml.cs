using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Passports
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int PassportId { get; set; }

        public string Name { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var passport = await _context.Passports
                .Where(p => p.Id == id && p.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (passport == null)
            {
                return NotFound();
            }

            PassportId = passport.Id;
            Name = passport.Name;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var passport = await _context.Passports
                .Where(p => p.Id == id && p.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (passport == null)
            {
                return NotFound();
            }

            // Delete the linked automatic reminder first
            var linkedReminder = await _context.Reminders
                .FirstOrDefaultAsync(r => r.PassportId == passport.Id && r.IsAutomatic == true);

            if (linkedReminder != null)
            {
                _context.Reminders.Remove(linkedReminder);
            }

            // Delete the passport
            _context.Passports.Remove(passport);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Passport deleted successfully";

            return RedirectToPage("./Index");
        }
    }
}
